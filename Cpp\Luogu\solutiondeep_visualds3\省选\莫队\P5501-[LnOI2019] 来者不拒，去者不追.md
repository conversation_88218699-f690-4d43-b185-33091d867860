# 题目信息

# [LnOI2019] 来者不拒，去者不追

## 题目背景

题目提供者：朝田诗乃

![avartar](https://cdn.luogu.com.cn/upload/pic/66100.png)

## 题目描述

给定一个长度为 $n$ 的序列 $a$。给定 $m$ 个询问，每次询问一个区间中 $[l,r]$ 中所有数的“Abbi 值”之和。

Abbi 值定义为：若 $a_i$ 在询问区间 $[l,r]$ 中是第 $k$ 小，那么它的“Abbi 值”等于 $k \times a_i$。

为了消除歧义举个例子：

有序列 $\{1,2,2,3\}$，那么 $1$ 是第 $1$ 小，$2$ 是第 $2$ 小，$3$ 是第 $4$ 小，序列 Abbi 值和为：

$$1 \times 1+2 \times 2+2 \times 2+3 \times 4=21.$$

## 说明/提示

前 2 个数据点，$1≤n,m≤1000$，时限 1s。

接下来 14 个数据点，$1≤n,a_i,m≤100000$，$1≤l≤r≤n$，时限 1s。

最后两个数据点，$1≤a_i≤100000$，$1≤l≤r≤n$，$1≤n,m≤500000$，时限 3s。

建议使用读入优化。建议开启 O2 优化。

数据已经过加强。

## 样例 #1

### 输入

```
4 2
1 2 2 3
1 4
1 2```

### 输出

```
21
5```

## 样例 #2

### 输入

```
10 5
8 6 9 8 1 1 3 10 7 9
5 8
1 3
5 7
9 9
5 6
```

### 输出

```
51
49
11
7
2
```

# AI分析结果


### 题目分析
本题需要计算区间内所有数的"Abbi值"之和（Abbi值定义为该数在区间排序后的排名乘以数值）。数据范围较大（n, m ≤ 500,000），直接使用普通莫队或树状数组会超时。通过二次离线莫队算法，将时间复杂度优化至 O(n√n)。

### 算法核心思路
1. **问题转化**：每个数x的贡献为 `x * (区间内比x小的数的个数 + 1) + 区间内比x大的数的和`。
2. **二次离线莫队**：
   - 将莫队指针移动产生的贡献分解为前缀和形式。
   - 预处理部分信息（f[i]: [1,i-1]中比a_i大的数的和；g[i]: [1,i-1]中比a_i小的数的个数）。
   - 值域分块维护值域信息，实现O(1)查询和O(√n)修改。
3. **贡献计算**：
   - 右指针移动：贡献 = f[r] + a_r*(g[r]+1) - [二次离线部分]
   - 左指针移动：贡献 = suf[l] - [二次离线部分]
4. **二次离线处理**：
   - 扫描数组，用值域分块动态维护信息。
   - 处理保存的询问区间，计算增量贡献。

### 核心代码实现
```cpp
#include <bits/stdc++.h>
using namespace std;
typedef long long ll;
const int N = 5e5 + 5, M = 1e5 + 5, BLK = 317;

int n, m, a[N], bel[M], L[BLK], R[BLK], cnt[BLK], tot;
ll f[N], g[N], pre[N], suf[N], ans[N], sum[BLK], s[M];
struct Query { int l, r, id; ll ans; } q[N];
struct Node { int l, r, id, op; };
vector<Node> LQ[N], RQ[N];

void initBlock() {
    for (int i = 1; i <= M; i++) bel[i] = (i - 1) / BLK + 1;
    for (int i = 1; i <= BLK; i++) {
        L[i] = R[i - 1] + 1;
        R[i] = min(i * BLK, M);
    }
    tot = BLK;
}

void add(int x) {
    int bl = bel[x];
    for (int i = x; i <= R[bl]; i++) s[i]++;
    for (int i = bl + 1; i <= tot; i++) cnt[i]++;
    for (int i = x; i >= L[bl]; i--) sum[bl] += x;
    for (int i = bl - 1; i >= 1; i--) sum[i] += x;
}

ll querySmall(int x) { return s[x] + cnt[bel[x]]; }
ll queryLarge(int x) { return sum[bel[x]] - s[x] * x; }

int main() {
    scanf("%d%d", &n, &m);
    initBlock();
    for (int i = 1; i <= n; i++) scanf("%d", &a[i]);

    // 预处理前缀贡献
    for (int i = 1; i <= n; i++) {
        f[i] = (querySmall(a[i] - 1) + 1) * a[i] + queryLarge(a[i] + 1);
        add(a[i]);
        pre[i] = pre[i - 1] + f[i];
    }

    // 初始化值域分块
    memset(cnt, 0, sizeof(cnt));
    memset(s, 0, sizeof(s));
    memset(sum, 0, sizeof(sum));

    // 预处理后缀贡献
    for (int i = n; i >= 1; i--) {
        f[i] = (querySmall(a[i] - 1) + 1) * a[i] + queryLarge(a[i] + 1);
        add(a[i]);
        suf[i] = suf[i + 1] + f[i];
    }

    // 读入查询并排序
    for (int i = 1; i <= m; i++) {
        scanf("%d%d", &q[i].l, &q[i].r);
        q[i].id = i;
    }
    sort(q + 1, q + m + 1, [](const Query &a, const Query &b) {
        return a.l / BLK == b.l / BLK ? 
            (a.l / BLK & 1 ? a.r < b.r : a.r > b.r) : a.l < b.l;
    });

    // 莫队指针移动并记录二次离线区间
    int l = 1, r = 0;
    for (int i = 1; i <= m; i++) {
        if (r < q[i].r) {
            LQ[l - 1].push_back({r + 1, q[i].r, i, -1});
            q[i].ans += pre[q[i].r] - pre[r];
            r = q[i].r;
        }
        if (r > q[i].r) {
            LQ[l - 1].push_back({q[i].r + 1, r, i, 1});
            q[i].ans -= pre[r] - pre[q[i].r];
            r = q[i].r;
        }
        if (l < q[i].l) {
            RQ[r + 1].push_back({l, q[i].l - 1, i, -1});
            q[i].ans += suf[l] - suf[q[i].l];
            l = q[i].l;
        }
        if (l > q[i].l) {
            RQ[r + 1].push_back({q[i].l, l - 1, i, 1});
            q[i].ans -= suf[q[i].l] - suf[l];
            l = q[i].l;
        }
    }

    // 二次离线处理（从左向右扫描）
    memset(cnt, 0, sizeof(cnt));
    memset(s, 0, sizeof(s));
    memset(sum, 0, sizeof(sum));
    for (int i = 1; i <= n; i++) {
        add(a[i]);
        for (auto &nd : LQ[i]) {
            ll tmp = 0;
            for (int j = nd.l; j <= nd.r; j++) {
                tmp += (querySmall(a[j] - 1) + 1) * a[j] + queryLarge(a[j] + 1);
            }
            q[nd.id].ans += nd.op * tmp;
        }
    }

    // 二次离线处理（从右向左扫描）
    memset(cnt, 0, sizeof(cnt));
    memset(s, 0, sizeof(s));
    memset(sum, 0, sizeof(sum));
    for (int i = n; i >= 1; i--) {
        add(a[i]);
        for (auto &nd : RQ[i]) {
            ll tmp = 0;
            for (int j = nd.l; j <= nd.r; j++) {
                tmp += (querySmall(a[j] - 1) + 1) * a[j] + queryLarge(a[j] + 1);
            }
            q[nd.id].ans += nd.op * tmp;
        }
    }

    // 整合答案
    for (int i = 1; i <= m; i++) {
        if (i > 1) q[i].ans += q[i - 1].ans;
        ans[q[i].id] = q[i].ans;
    }
    for (int i = 1; i <= m; i++) {
        printf("%lld\n", ans[i]);
    }
    return 0;
}
```

### 算法可视化
1. **值域分块示意图**：
   ```
   值域[1, 100000]分为317块（每块约316元素）
   ┌─────┬─────┬─────┬───┐
   │Block1│Block2│ ... │Blk317
   └─────┴─────┴─────┴───┘
   每块维护：块内元素个数(cnt)、块内和(sum)
   ```

2. **二次离线扫描过程**：
   - **从左向右扫描**：
     - 更新值域分块（添加当前数）
     - 处理右指针移动产生的二次离线区间
   - **从右向左扫描**：
     - 更新值域分块（添加当前数）
     - 处理左指针移动产生的二次离线区间

3. **贡献计算示例**：
   - 添加数x=5时：
     - 更新值域分块（第2块）
     - 计算影响：比5小的数有3个 → 贡献3×5
     - 比5大的数和为10 → 总贡献=15+10=25

### 复杂度分析
- **时间复杂度**：O(n√n)
  - 预处理：O(n log n)
  - 莫队排序：O(m log m)
  - 二次离线：O(n√n)（n次O(√n)修改 + n√n次O(1)查询）
- **空间复杂度**：O(n + m)（存储查询和分块信息）

### 拓展练习
1. **相似题目**：
   - P4887 莫队二次离线（模板题）
   - P5047 区间逆序对（二次离线应用）
   - P5398 区间倍数对计数（值域分块应用）
   
2. **洛谷推荐**：
   - P1903 数颜色（带修莫队）
   - P5268 简单数据结构（值域分块练习）
   - P4887 二次离线模板题（巩固算法）

> 提示：本题的核心在于巧妙利用值域分块将单次查询复杂度降为O(1)，并通过二次离线处理莫队指针移动。掌握值域分块和二次离线思想是解决此类问题的关键。

---
处理用时：128.83秒